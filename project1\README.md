# Project 1: Scaffolding a FastAPI Project

This project documents the process of setting up and scaffolding a new FastAPI application.

## Steps

1. Set up a virtual environment

    ```PowerShell
    py -m venv project1_v_env
    .\project1_v_env\Scripts\activate
    ```

2. Attempt to scaffold using create-fastapi-project

    ```PowerShell
    py -m pip install create-fastapi-project
    create-fastapi-project
    ```

> **Note:** The `create-fastapi-project` command did not work as expected on Windows. Consider using WSL (Windows Subsystem for Linux) or an alternative approach like manually setting up the project structure.

3. Attempt to scaffold using fastapi-utilities

    ```PowerShell
    py -m pip install fastapi-utilities
    py -m fastapi_utilities init
    ```

> **Note:** The `fastapi_utilities` command seems to work. And it did generate the below project structure:

3.1. Generated Backend Project Structure

    ```
    project1\backend\
    ├── README.md                    # Project documentation and setup instructions
    ├── requirements.txt             # Python dependencies (FastAPI, Uvicorn, SQLModel)
    ├── src\                        # Main source code directory
    │   ├── __init__.py             # Package initialization
    │   ├── main.py                 # FastAPI application entry point
    │   ├── api.py                  # Main API router configuration
    │   ├── database.py             # Database connection and session management
    │   ├── constants.py            # Global constants (DATABASE_URL)
    │   └── module1\                # Feature module
    │       ├── __init__.py
    │       ├── constants.py        # Module-specific constants
    │       ├── models.py           # Database models
    │       ├── schemas.py          # Pydantic schemas for API
    │       ├── service.py          # Business logic
    │       ├── utils.py            # Utility functions
    │       └── views.py            # API endpoints/routes
    └── tests\                      # Test directory
        ├── __init__.py
        └── test_module1.py         # Tests for module1
    ```

3.2. Project Summary

This is a **FastAPI backend project** with the following characteristics:

- **Database**: Uses SQLAlchemy/SQLModel for ORM
- **Architecture**: Modular structure with separate modules for different features
- **Structure**: Follows a clean architecture pattern with separation of concerns:
  - `models.py` - Database models
  - `schemas.py` - API request/response schemas
  - `views.py` - API endpoints
  - `service.py` - Business logic
  - `utils.py` - Helper functions
- **Testing**: Has a dedicated tests directory
- **Entry Point**: `src/main.py` with the FastAPI app
- **Port**: Runs on port 8000 by default

3.3. PowerShell Commands for Project Exploration

Since you're on Windows using PowerShell, here are useful commands to explore the project structure:

3.3.1.  Tree-like structure view:

    ```powershell
    tree "project1\backend" /F
    ```

