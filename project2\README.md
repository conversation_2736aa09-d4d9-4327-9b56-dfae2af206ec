# Project 2: Manually Setting Up a FastAPI Project

This project documents the process of manually setting up a FastAPI application without using scaffolding tools.

## Steps

1. Set up a virtual environment

   ```PowerShell
   py -m venv project2_v_env
   .\project2_v_env\Scripts\activate
   ```

2. Minimal FastAPI project structure

   1. Create `main.py` and `requirements.txt` files

      ```PowerShell
      cd project2
      New-Item -Name "main.py" -ItemType "File"
      New-Item -Name "requirements.txt" -ItemType "File"
      ```

   2. Project structure

      ```PowerShell
      my_fastapi_app/
      ├── main.py               # Entry point of your app
      ├── requirements.txt      # Project dependencies
      ```

3. Add the following content to `requirements.txt`:

   ```plaintext
   fastapi[standard]
   uvicorn[standard]
   ```

4. Install the dependencies:

   ```PowerShell
   py -m pip install -r requirements.txt
   ```

5. Add the following content to `main.py`:

   ```python
   from fastapi import FastAPI

   app = FastAPI()

   @app.get("/")
   def read_root():
       return {"message": "Hello, FastAPI beginner!"}
   ```

6. Run the FastAPI application using Uvicorn:

   ```PowerShell
   uvicorn main:app --reload
   ```

7. Or Run the FastAPI application using the FastAPI CLI:

   ```PowerShell
    fastapi dev main.py # without the --reload flag
    fastapi run main.py --reload # with the --reload flag
   ```

**Note:** The application will start and provide 2 endpoints:
- Server started at http://127.0.0.1:8000
- Documentation at http://127.0.0.1:8000/docs

8. For production executing fastapi run starts FastAPI in production mode by default

    ```PowerShell
    fastapi run main.py
    ```


