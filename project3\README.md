# Project 3: Manually Setting Up a FastAPI Project with SQLite and CRUD Operations

This project demonstrates how to manually set up a FastAPI application with SQLite and implement basic CRUD operations.


## Steps

1. Set up a virtual environment

   ```PowerShell
   py -m venv project3_v_env
   .\project3_v_env\Scripts\activate
   ```

2. Set the below project structure:

   ```
   project3/
    ├── main.py              # Entry point of your app
    ├── database.py     # Database connection and session management
    ├── models.py         # Database models
    ├── schemas.py     # Pydantic schemas for API
    ├── crud.py         # CRUD operations
    ├── requirements.txt     # Project dependencies
   ```

3. Create the files `main.py`, `database.py`, `models.py`, `schemas.py`, and `crud.py` in the `project3` directory.

    ```PowerShell
    New-Item -Name "main.py" -ItemType "File"
    New-Item -Name "database.py" -ItemType "File"
    New-Item -Name "models.py" -ItemType "File"
    New-Item -Name "schemas.py" -ItemType "File"
    New-Item -Name "crud.py" -ItemType "File"
    New-Item -Name "requirements.txt" -ItemType "File"
    ```

4. Add the following content to `requirements.txt`:

   ```plaintext
    fastapi[standard]
    uvicorn[standard]
    sqlalchemy
    pydantic
    ```

5. Install the dependencies:
    ```PowerShell
    py -m pip install -r requirements.txt
    ```

6. Add the following content to `database.py` to Set up the SQLite DB with SQLAlchemy:

    ```python
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker, declarative_base

    DATABASE_URL = "sqlite:///./test.db"

    engine = create_engine(
        DATABASE_URL, connect_args={"check_same_thread": False}  # needed for SQLite
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    Base = declarative_base()
    ```

7. Add the following content to `models.py` to define SQLAlchemy ORM model:

    ```python
    from sqlalchemy import Column, Integer, String
    from database import Base

    class Item(Base):
        __tablename__ = "items"

        id = Column(Integer, primary_key=True, index=True)
        title = Column(String, index=True)
        description = Column(String, index=True)
    ```

8. Add the following content to `schemas.py` to define Pydantic schemas(Validation Layer):

    ```python
    from pydantic import BaseModel

    class ItemBase(BaseModel):
        title: str
        description: str

    class ItemCreate(ItemBase):
        pass

    class Item(ItemBase):
        id: int

        class Config:
            orm_mode = True
    ```

9. Add the following content to `crud.py` to implement CRUD operations:

    ```python
    from sqlalchemy.orm import Session
    import models, schemas


    def get_item(db: Session, item_id: int):
        return db.query(models.Item).filter(models.Item.id == item_id).first()


    def get_items(db: Session, skip: int = 0, limit: int = 10):
        return db.query(models.Item).offset(skip).limit(limit).all()


    def create_item(db: Session, item: schemas.ItemCreate):
        db_item = models.Item(**item.model_dump())
        db.add(db_item)
        db.commit()
        db.refresh(db_item)
        return db_item
    ```

10. Add the following content to `main.py` to create the FastAPI app and define endpoints:

    ```python
    from fastapi import FastAPI, Depends, HTTPException
    from sqlalchemy.orm import Session

    import models, schemas, crud
    from database import SessionLocal, engine

    models.Base.metadata.create_all(bind=engine)

    app = FastAPI()


    # Dependency
    def get_db():
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()


    @app.post("/items/", response_model=schemas.Item)
    def create_item(item: schemas.ItemCreate, db: Session = Depends(get_db)):
        return crud.create_item(db=db, item=item)


    @app.get("/items/", response_model=list[schemas.Item])
    def read_items(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
        return crud.get_items(db, skip=skip, limit=limit)


    @app.get("/items/{item_id}", response_model=schemas.Item)
    def read_item(item_id: int, db: Session = Depends(get_db)):
        db_item = crud.get_item(db, item_id=item_id)
        if db_item is None:
            raise HTTPException(status_code=404, detail="Item not found")
        return db_item

11. Start the FastAPI application using fastapi dev command:

   ```PowerShell
    fastapi dev main.py # without the --reload flag
    fastapi run main.py --reload # with the --reload flag
   ```
12. to manually test the CRUD operations, you can use a tool like cURL to send HTTP requests to the endpoints defined in `main.py`. 

- For example, you can create an item with a POST request:

    ```bash
    curl -X POST "http://127.0.0.1:8000/items/" -H "Content-Type: application/json" -d '{"title": "Item 1", "description": "Description for Item 1"}'
    ```

- To read items, you can use a GET request:

    ```bash
    curl -X GET "http://127.0.0.1:8000/items/"
    ```

- To read a specific item by ID, you can use:

    ```bash
    curl -X GET "http://127.0.0.1:8000/items/1"
    ```

# Architectural Overview

*Analysis by Principal Software Architect - 15+ Years Backend Systems Experience*

## High-Level Technical Analysis

### Module Responsibilities and Separation of Concerns

This FastAPI application demonstrates exemplary **Separation of Concerns (SoC)** through a well-structured modular architecture. Each module has a distinct responsibility within the system:

#### **`database.py` - Data Access Layer**
- **Primary Responsibility**: Database connection management and session configuration
- **Key Components**:
  - `create_engine()`: Establishes database connectivity with SQLite-specific configurations
  - `SessionLocal`: Factory pattern for database session creation with proper transaction handling
  - `Base`: Declarative base class enabling SQLAlchemy's ORM metadata tracking
- **Design Patterns**: Factory Pattern, Singleton Pattern (engine instance)
- **Architectural Role**: Foundation layer that abstracts database connectivity concerns

#### **`models.py` - Data Model Layer**
- **Primary Responsibility**: Database schema definition and ORM mapping
- **Key Components**:
  - `Item` class: SQLAlchemy model defining table structure, constraints, and indexes
  - Metadata definitions that drive automatic table creation
- **Design Patterns**: Active Record Pattern (via SQLAlchemy ORM)
- **Architectural Role**: Domain model that bridges object-oriented code with relational database structures

#### **`schemas.py` - Data Transfer Object (DTO) Layer**
- **Primary Responsibility**: API contract definition and data validation
- **Key Components**:
  - `ItemBase`: Common field definitions following DRY principles
  - `ItemCreate`: Input validation schema for POST operations
  - `Item`: Response serialization schema with ORM compatibility
- **Design Patterns**: Data Transfer Object Pattern, Template Method Pattern (inheritance hierarchy)
- **Architectural Role**: API boundary layer ensuring type safety and data integrity

#### **`crud.py` - Business Logic Layer**
- **Primary Responsibility**: Database operations abstraction and business logic encapsulation
- **Key Components**:
  - Database operation functions with standardized signatures
  - Query optimization through pagination parameters
  - Transaction management coordination
- **Design Patterns**: Repository Pattern, Command Pattern
- **Architectural Role**: Service layer that encapsulates business rules and database interaction logic

#### **`main.py` - Presentation Layer**
- **Primary Responsibility**: HTTP request/response handling and API endpoint definition
- **Key Components**:
  - FastAPI application instance and configuration
  - Route definitions with proper HTTP verb mapping
  - Dependency injection orchestration
- **Design Patterns**: Dependency Injection, Model-View-Controller (MVC)
- **Architectural Role**: API gateway that coordinates between web layer and business logic

### Dependency Injection Architecture

The application leverages **FastAPI's Dependency Injection** system to achieve **Inversion of Control (IoC)**, which provides:

#### **Session Management via `get_db()` Dependency**
```python
def get_db():
    db = SessionLocal()
    try:
        yield db  # Generator pattern for resource management
    finally:
        db.close()  # Guaranteed cleanup via try/finally
```

**Benefits Achieved**:
- **Resource Management**: Automatic database connection lifecycle management
- **Testability**: Easy mocking of database sessions for unit testing
- **Scalability**: Connection pooling and proper resource cleanup
- **Error Handling**: Guaranteed session cleanup even during exceptions

#### **Dependency Declaration Pattern**
```python
def create_item(item: schemas.ItemCreate, db: Session = Depends(get_db)):
```

This pattern ensures:
- **Type Safety**: Full type hints enable IDE support and static analysis
- **Automatic Injection**: FastAPI automatically provides dependencies
- **Request Isolation**: Each HTTP request receives its own database session
- **Transaction Boundaries**: Natural transaction scope per HTTP request

### Data Validation and Serialization Pipeline

The application implements a comprehensive **data validation pipeline** using Pydantic:

#### **Input Validation Flow**
1. **HTTP Request** → Raw JSON payload
2. **Pydantic Schema** → Type validation and parsing via `schemas.ItemCreate`
3. **Business Logic** → Validated data passed to CRUD operations
4. **Database Layer** → Type-safe database operations

#### **Output Serialization Flow**
1. **Database Model** → SQLAlchemy ORM object
2. **Pydantic Schema** → Automatic serialization via `response_model=schemas.Item`
3. **JSON Response** → FastAPI automatic JSON encoding
4. **Client** → Type-safe API response

#### **Key Validation Features**
- **Type Coercion**: Automatic type conversion where safe
- **Field Validation**: Built-in validation for required fields and data types
- **Error Handling**: Structured validation error responses
- **ORM Integration**: Seamless conversion between SQLAlchemy models and Pydantic schemas via `Config.orm_mode`

## Request Flow Visualization

### POST /items/ Request Sequence

The complete end-to-end flow for creating a new item is documented in the sequence diagram:

📊 **[View POST /items/ Sequence Diagram](./diagrams/post-items-sequence.md)**

### Component Dependencies Architecture

The static relationships and dependencies between all five modules are visualized in the component diagram:

📊 **[View Component Dependencies Diagram](./diagrams/component-dependencies.md)**

## Endpoint Breakdown

### POST /items/ - Create Item Workflow

**Step-by-Step Orchestration:**

1. **Request Reception** (`main.py`)
   - FastAPI receives HTTP POST request with JSON payload
   - Routes to `create_item()` path operation function

2. **Dependency Resolution** (`main.py` → `database.py`)
   - FastAPI invokes `get_db()` dependency
   - `SessionLocal()` creates new database session
   - Session passed to path operation function

3. **Input Validation** (`main.py` → `schemas.py`)
   - JSON payload automatically parsed against `schemas.ItemCreate`
   - Pydantic validates field types and required constraints
   - Returns validated Python object or 422 validation error

4. **Business Logic Delegation** (`main.py` → `crud.py`)
   - Path operation calls `crud.create_item(db, item)`
   - Passes database session and validated item data

5. **Data Transformation** (`crud.py` → `models.py`)
   - `item.model_dump()` converts Pydantic schema to dictionary
   - `models.Item(**item_dict)` creates SQLAlchemy ORM instance

6. **Database Transaction** (`crud.py` → `database.py` → SQLite)
   - `db.add(db_item)` stages object for insertion
   - `db.commit()` executes transaction to database
   - `db.refresh(db_item)` retrieves auto-generated ID

7. **Response Serialization** (`main.py` → `schemas.py`)
   - SQLAlchemy object automatically converted via `response_model=schemas.Item`
   - Pydantic serializes to JSON with proper type conversion

8. **Session Cleanup** (`main.py` → `database.py`)
   - `finally` block in `get_db()` ensures `db.close()` is called
   - Database connection returned to pool

### GET /items/ - List Items Workflow

**Step-by-Step Orchestration:**

1. **Request Processing** (`main.py`)
   - Query parameters `skip` and `limit` extracted with defaults (0, 10)
   - Database session injected via `Depends(get_db)`

2. **Query Execution** (`main.py` → `crud.py`)
   - `crud.get_items(db, skip, limit)` called with pagination parameters

3. **Database Query** (`crud.py` → `models.py` → SQLite)
   - `db.query(models.Item)` constructs base query
   - `.offset(skip)` implements pagination offset
   - `.limit(limit)` restricts result set size
   - `.all()` executes query and returns list of SQLAlchemy objects

4. **Bulk Serialization** (`main.py` → `schemas.py`)
   - `response_model=list[schemas.Item]` serializes entire list
   - Each item converted from SQLAlchemy to Pydantic to JSON

### GET /items/{item_id} - Retrieve Single Item Workflow

**Step-by-Step Orchestration:**

1. **Path Parameter Extraction** (`main.py`)
   - FastAPI extracts `item_id` from URL path
   - Automatic type conversion to `int` with validation

2. **Query Execution** (`main.py` → `crud.py`)
   - `crud.get_item(db, item_id)` called with extracted ID

3. **Targeted Database Query** (`crud.py` → `models.py` → SQLite)
   - `db.query(models.Item).filter(models.Item.id == item_id)` constructs WHERE clause
   - `.first()` returns single object or `None`

4. **Error Handling** (`main.py`)
   - Check if `db_item is None`
   - Raise `HTTPException(status_code=404)` for missing items
   - FastAPI automatically converts to proper HTTP 404 response

5. **Single Item Serialization** (`main.py` → `schemas.py`)
   - Found item serialized via `response_model=schemas.Item`
   - Includes auto-generated ID field in response

---

*This architectural analysis demonstrates enterprise-grade separation of concerns, proper dependency management, and scalable design patterns suitable for production systems. The modular structure enables independent testing, maintenance, and future enhancements while maintaining clean boundaries between layers.*