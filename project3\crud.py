# Session: SQLAlchemy's database session class that manages database transactions
# A Session establishes a connection to the database and provides methods for
# querying, adding, updating, and deleting records. It acts as a "workspace"
# for database operations and handles transaction management (commit/rollback)
from sqlalchemy.orm import Session
import models, schemas

# CRUD: Create, Read, Update, Delete operations for the Item model
# These functions provide a clean interface between FastAPI endpoints and the database


# db: Session - This is Python type hinting syntax
# 'db' is the parameter name, 'Session' is the type annotation
# Type hints improve code readability, enable IDE autocomplete, and help catch errors
# Here it tells us that 'db' should be a SQLAlchemy Session object
def get_item(db: Session, item_id: int):
    # Uses the Session to query the database for a specific item by ID
    # .filter() adds a WHERE clause, .first() returns the first match or None
    return db.query(models.Item).filter(models.Item.id == item_id).first()


# skip and limit parameters enable pagination for large datasets
def get_items(db: Session, skip: int = 0, limit: int = 10):
    # .offset(skip) skips the first 'skip' records (for pagination)
    # .limit(limit) restricts the number of returned records
    # .all() executes the query and returns all matching records as a list
    return db.query(models.Item).offset(skip).limit(limit).all()


# item: schemas.ItemCreate - expects a Pydantic schema for data validation
def create_item(db: Session, item: schemas.ItemCreate):
    # Convert Pydantic schema to dictionary using model_dump()
    # **item.model_dump() unpacks the dictionary as keyword arguments to create the SQLAlchemy model
    db_item = models.Item(**item.model_dump())

    # Add the new item to the session (stages it for database insertion)
    db.add(db_item)

    # Commit the transaction to actually save the item to the database
    db.commit()

    # Refresh the object to get the auto-generated ID from the database
    db.refresh(db_item)

    return db_item
