# SQLAlchemy: A Python SQL toolkit and Object-Relational Mapping (ORM) library
# that provides a full suite of well-known enterprise-level persistence patterns,
# designed for efficient and high-performing database access

from sqlalchemy import create_engine

# sqlalchemy.orm: The Object-Relational Mapping module of SQLAlchemy
# Provides tools for mapping Python classes to database tables and managing
# database sessions for CRUD operations
from sqlalchemy.orm import sessionmaker, declarative_base

# Database connection string - using SQLite for this project
DATABASE_URL = "sqlite:///./test.db"

# create_engine: Creates a database engine - the core interface to the database
# The engine manages connections, connection pooling, and SQL dialect handling
# For SQLite, we need check_same_thread=False to allow multiple threads
engine = create_engine(
    DATABASE_URL, connect_args={"check_same_thread": False}  # needed for SQLite
)

# sessionmaker: A factory for creating database session objects
# Sessions manage transactions and provide the interface for querying and
# persisting data. In FastAPI, each request will get its own session instance
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# declarative_base: Creates a base class for defining ORM models
# All database models/tables will inherit from this Base class
# This enables SQLAlchemy to track model definitions and create table schemas
Base = declarative_base()
