# Component Dependencies Architecture Diagram

This diagram visualizes the static relationships and dependencies between all five modules in the FastAPI application.

```mermaid
classDiagram
    class Main {
        +FastAPI app
        +get_db() Session
        +create_item()
        +read_items()
        +read_item()
    }
    
    class Database {
        +create_engine()
        +SessionLocal
        +Base
        -DATABASE_URL
    }
    
    class Models {
        +Item
        -__tablename__
        -id: Column
        -title: Column
        -description: Column
    }
    
    class Schemas {
        +ItemBase
        +ItemCreate
        +Item
        +Config
    }
    
    class CRUD {
        +get_item()
        +get_items()
        +create_item()
    }
    
    %% Import Dependencies
    Main --> Database : imports SessionLocal, engine
    Main --> Models : imports models
    Main --> Schemas : imports schemas
    Main --> CRUD : imports crud
    
    CRUD --> Models : imports models
    CRUD --> Schemas : imports schemas
    CRUD --> Database : uses Session type
    
    Models --> Database : inherits from Base
    
    %% Composition Relationships
    Main *-- Database : manages sessions
    Main *-- CRUD : delegates operations
    CRUD o-- Models : operates on
    Main o-- Schemas : validates with
```
