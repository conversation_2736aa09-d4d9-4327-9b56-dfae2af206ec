# POST /items/ Request Sequence Diagram

This diagram illustrates the complete end-to-end flow for creating a new item via the POST /items/ endpoint.

```mermaid
sequenceDiagram
    participant Client
    participant FastAPI
    participant PathOp as Path Operation<br/>(main.py)
    participant CRUD as CRUD Function<br/>(crud.py)
    participant ORM as SQLAlchemy ORM
    participant DB as SQLite Database

    Client->>+FastAPI: POST /items/<br/>{"title": "Item 1", "description": "Description"}
    
    Note over FastAPI: Dependency Injection
    FastAPI->>+PathOp: get_db() → SessionLocal()
    
    Note over FastAPI: Request Validation
    FastAPI->>+PathOp: Validate JSON against<br/>schemas.ItemCreate
    
    PathOp->>+CRUD: crud.create_item(db, validated_item)
    
    Note over CRUD: Data Transformation
    CRUD->>CRUD: item.model_dump()<br/>Convert Pydantic → dict
    
    CRUD->>+ORM: models.Item(**item_dict)
    ORM->>ORM: Create SQLAlchemy instance
    
    CRUD->>ORM: db.add(db_item)
    CRUD->>ORM: db.commit()
    
    ORM->>+DB: INSERT INTO items<br/>(title, description)
    DB-->>-ORM: Auto-generated ID returned
    
    CRUD->>ORM: db.refresh(db_item)
    ORM->>+DB: SELECT to get complete record
    DB-->>-ORM: Full record with ID
    ORM-->>-CRUD: Updated SQLAlchemy object
    
    CRUD-->>-PathOp: Return db_item
    
    Note over PathOp: Response Serialization
    PathOp->>PathOp: Convert via<br/>response_model=schemas.Item
    
    PathOp-->>-FastAPI: Serialized Item response
    FastAPI-->>-Client: HTTP 200<br/>{"id": 1, "title": "Item 1", "description": "Description"}
```
