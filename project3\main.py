# FastAPI: Modern, fast web framework for building APIs with Python based on standard Python type hints
# Depends: Dependency injection system that allows sharing common logic across endpoints
# HTTPException: FastAPI's way to return HTTP error responses with status codes and messages
from fastapi import FastAPI, Depends, HTTPException
from sqlalchemy.orm import Session

import models, schemas, crud

# SessionLocal: Factory for creating database sessions (from our database.py)
# engine: Database engine that manages connections (from our database.py)
from database import SessionLocal, engine

# models.Base.metadata.create_all(bind=engine): Creates all database tables
# Uses the metadata from our Base class (defined in database.py) to create tables
# Reads all model definitions (like Item in models.py) and creates corresponding database tables
# Only creates tables that don't already exist - safe to run multiple times
models.Base.metadata.create_all(bind=engine)

# app = FastAPI(): Creates the main FastAPI application instance
# This is the core object that handles all HTTP requests, routing, and responses
# All API endpoints will be registered with this app instance
app = FastAPI()


# Dependency Injection Pattern: get_db() function provides database sessions to endpoints
# FastAP<PERSON> will call this function for each request that needs a database connection
def get_db():
    # Create a new database session for this request
    db = SessionLocal()
    try:
        # yield: Makes this a generator function - provides the session to the endpoint
        # The endpoint function receives 'db' and can use it for database operations
        yield db
    finally:
        # Ensures the database session is always closed after the request completes
        # Prevents connection leaks and ensures proper cleanup
        db.close()


# response_model=schemas.Item: Tells FastAPI what schema to use for the response
# FastAPI will automatically convert the returned data to JSON using this schema
# Also generates API documentation showing the expected response structure
@app.post("/items/", response_model=schemas.Item)
# db: Session = Depends(get_db): Dependency injection - FastAPI calls get_db()
# and passes the resulting session as the 'db' parameter to this function
def create_item(item: schemas.ItemCreate, db: Session = Depends(get_db)):
    return crud.create_item(db=db, item=item)


# response_model=list[schemas.Item]: Response will be a list of Item schemas
# FastAPI will serialize each item in the list using the schemas.Item model
@app.get("/items/", response_model=list[schemas.Item])
def read_items(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
    return crud.get_items(db, skip=skip, limit=limit)


@app.get("/items/{item_id}", response_model=schemas.Item)
def read_item(item_id: int, db: Session = Depends(get_db)):
    db_item = crud.get_item(db, item_id=item_id)
    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")
    return db_item
