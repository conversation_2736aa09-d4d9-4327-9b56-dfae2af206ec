# Pydantic: A Python library for data validation and parsing using Python type hints
# It automatically validates incoming data, converts types, and provides clear error messages
# Essential for FastAPI to handle request/response data validation and serialization
from pydantic import BaseModel


# BaseModel: The foundation class for creating Pydantic models/schemas
# Provides automatic validation, serialization, and type conversion based on type hints
# Used to define the structure and validation rules for API request/response data
# ItemBase: Base schema containing common fields shared by all Item-related schemas
# Contains the core data fields that both input and output schemas will use
# Follows DRY principle by defining shared attributes once
class ItemBase(BaseModel):
    title: str
    description: str


# ItemCreate: Schema for creating new items via POST requests
# Inherits from ItemBase to get title and description fields
# Used for request validation when clients send data to create new items
# Note: No 'id' field since the database auto-generates it
class ItemCreate(ItemBase):
    pass


# Item: Schema for returning item data in API responses
# Includes the 'id' field that gets added after database creation
# Used when FastAPI returns item data to clients (GET requests)
class Item(ItemBase):
    id: int

    # Config: Inner class that configures Pydantic model behavior
    # orm_mode=True: Enables compatibility with SQLAlchemy ORM objects
    # Allows Pydantic to read data from SQLAlchemy models (not just dictionaries)
    # Essential for converting database models to JSON responses in FastAPI
    class Config:
        orm_mode = True
