# Project 4: Same as Project 3 but manipulating the code for better understanding

This project builds upon the previous project (Project 3) but with alembic and basic crud operations.

## Steps

1. Set up a virtual environment

   ```PowerShell
   py -m venv project4_v_env
   .\project4_v_env\Scripts\activate
   ```

2. Install the required dependencies

   ```PowerShell
    pip install -r requirements.txt
    ```

3. Initialize Alembic for database migrations

    ```PowerShell
    alembic init alembic
    ```

4. Configure Alembic
    - Edit `alembic.ini` to set the database URL
    - Edit `alembic/env.py` to include your models

5. Edit the `alembic.ini` file to set the database URL

    ```ini
    sqlalchemy.url = sqlite:///./dev_db_0.db
    ```

6. Edit the `alembic/env.py` file to include your models

    ```python
    from .models import Base  # Adjust the import according to your project structure
    target_metadata = Base.metadata
    ```

7. Create migrations.py to run migrations

    ```python
    # migrations.py
    from alembic import command
    from alembic.config import Config


    def run_migrations():
        alembic_cfg = Config("alembic.ini")
        command.upgrade(alembic_cfg, "head")
    ```

8. Import and use the run_migrations function in your main application

    ```python
    import migrations
    migrations.run_migrations()

    app = FastAPI()
    # …rest of your routers / startup events…
    ```

9. To run the migrations manually and inspect the migrations, you can use the following command:

    ```PowerShell
    alembic init alembic  
    
    # Generate migration based on model changes (required after each model change)
    alembic revision --autogenerate -m "migration v01" 
    
    # Apply the latest migration to the database (required after creating a migration)
    alembic upgrade head  
    
    alembic downgrade -1  # to revert the last migration
    alembic history  # to see the migration history
    alembic current  # to see the current migration version
    alembic show <revision>  # to see the details of a specific migration
    alembic stamp head  # to mark the database as up-to-date without applying migrations
    alembic branches  # to see the branches in the migration history
    ```

> **IMPORTANT NOTE**: Automatic database migrations within FastAPI (via run_migrations) are currently not functioning as intended. For now, migrations must be created and applied manually using the commands below after each change to your models. Automation may be revisited in the future once a safe and reliable mechanism is validated.
