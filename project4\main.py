# FastAPI: Modern, fast web framework for building APIs with Python based on standard Python type hints
# Depends: Dependency injection system that allows sharing common logic across endpoints
# HTTPException: FastAPI's way to return HTTP error responses with status codes and messages
import json
from fastapi import FastAPI, Depends, File, HTTPException, UploadFile
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

import models, schemas, crud

# SessionLocal: Factory for creating database sessions (from our database.py)
# engine: Database engine that manages connections (from our database.py)
from database import SessionLocal, engine

# models.Base.metadata.create_all(bind=engine): Creates all database tables
# Uses the metadata from our Base class (defined in database.py) to create tables
# Reads all model definitions (like Item in models.py) and creates corresponding database tables
# Only creates tables that don't already exist - safe to run multiple times
models.Base.metadata.create_all(bind=engine)

# app = FastAPI(): Creates the main FastAPI application instance
# This is the core object that handles all HTTP requests, routing, and responses
# All API endpoints will be registered with this app instance
app = FastAPI()


# Dependency Injection Pattern: get_db() function provides database sessions to endpoints
# <PERSON>AP<PERSON> will call this function for each request that needs a database connection
def get_db():
    # Create a new database session for this request
    db = SessionLocal()
    try:
        # yield: Makes this a generator function - provides the session to the endpoint
        # The endpoint function receives 'db' and can use it for database operations
        yield db
    finally:
        # Ensures the database session is always closed after the request completes
        # Prevents connection leaks and ensures proper cleanup
        db.close()


# response_model=schemas.Item: Tells FastAPI what schema to use for the response
# FastAPI will automatically convert the returned data to JSON using this schema
# Also generates API documentation showing the expected response structure
@app.post("/items/", response_model=schemas.Item)
# db: Session = Depends(get_db): Dependency injection - FastAPI calls get_db()
# and passes the resulting session as the 'db' parameter to this function
def create_item(item: schemas.ItemCreate, db: Session = Depends(get_db)):
    return crud.create_item(db=db, item=item)


# response_model=list[schemas.Item]: Response will be a list of Item schemas
# FastAPI will serialize each item in the list using the schemas.Item model
@app.get("/items/", response_model=list[schemas.Item])
def read_items(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
    return crud.get_items(db, skip=skip, limit=limit)


@app.get("/items/{item_id}", response_model=schemas.Item)
def read_item(item_id: int, db: Session = Depends(get_db)):
    db_item = crud.get_item(db, item_id=item_id)
    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")
    return db_item


@app.delete("/items/delete/{item_id}", response_model=schemas.Item)
def delete_item(item_id: int, db: Session = Depends(get_db)):
    db_item = crud.delete_item(db, item_id=item_id)
    if db_item is None:
        raise HTTPException(status_code=404, detail="Item not found")
    return db_item


@app.post("/items/batch/", response_model=list[schemas.Item])
def create_items_batch(items: list[schemas.ItemCreate], db: Session = Depends(get_db)):
    """
    Create multiple items in a single transaction.

    This endpoint accepts a list of items and creates them all at once,
    which is more efficient than creating them individually for large batches.

    Args:
        items: List of item data to create
        db: Database session dependency

    Returns:
        List of created item objects with their database IDs
    """
    return crud.create_items_batch(db=db, items=items)


@app.delete("/items/batch/delete", response_model=list[schemas.Item])
def delete_all_items(db: Session = Depends(get_db)):
    """
    Delete all items from the items table.
    Returns a list of deleted items.
    """
    return crud.delete_all_items(db=db)


@app.post("/import/clients/", response_model=list[schemas.Client])
async def import_clients_json(
    file: UploadFile = File(...), db: Session = Depends(get_db)
):
    """
    Import clients from a JSON file. The file should contain a list of clients.
    """
    content = await file.read()
    try:
        clients_data = json.loads(content)
        if not isinstance(clients_data, list):
            raise ValueError("JSON must be a list of clients")
    except Exception as e:
        return JSONResponse(status_code=400, content={"detail": f"Invalid JSON: {e}"})
    try:
        created_clients = crud.import_clients_from_json(
            db=db, clients_data=clients_data
        )
    except Exception as e:
        return JSONResponse(status_code=400, content={"detail": f"Import failed: {e}"})
    return created_clients


@app.post("/clients/", response_model=schemas.Client)
def create_client(client: schemas.ClientCreate, db: Session = Depends(get_db)):
    return crud.create_client(db=db, client=client)


@app.get("/clients/", response_model=list[schemas.Client])
def read_clients(skip: int = 0, limit: int = 16, db: Session = Depends(get_db)):
    return crud.get_clients(db, skip=skip, limit=limit)


@app.get("/clients/{client_id}", response_model=schemas.Client)
def read_client(client_id: int, db: Session = Depends(get_db)):
    db_client = crud.get_client(db, client_id=client_id)
    if db_client is None:
        raise HTTPException(status_code=404, detail="Client not found")
    return db_client


@app.delete("/clients/delete/{client_id}", response_model=schemas.Client)
def delete_client(client_id: int, db: Session = Depends(get_db)):
    db_client = crud.delete_client(db, client_id=client_id)
    if db_client is None:
        raise HTTPException(status_code=404, detail="Client not found")
    return db_client


@app.post("/clients/batch/", response_model=list[schemas.Client])
def create_clients_batch(
    clients: list[schemas.ClientCreate], db: Session = Depends(get_db)
):
    """
    Create multiple clients in a single transaction.

    This endpoint accepts a list of clients and creates them all at once,
    which is more efficient than creating them individually for large batches.

    Args:
        clients: List of client data to create
        db: Database session dependency

    Returns:
        List of created client objects with their database IDs
    """
    return crud.create_clients_batch(db=db, clients=clients)


@app.delete("/clients/batch/delete", response_model=list[schemas.Client])
def delete_all_clients(db: Session = Depends(get_db)):
    """
    Delete all clients from the clients table.
    Returns a list of deleted clients.
    """
    return crud.delete_all_clients(db=db)
