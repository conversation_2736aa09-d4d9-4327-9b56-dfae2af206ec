# Column: Defines a database table column with specific data type and constraints
# Integer: SQLAlchemy data type for integer values (maps to database INTEGER type)
# String: SQLAlchemy data type for text/varchar values (maps to database VARCHAR/TEXT)
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from database import Base
from datetime import datetime, timezone


class Item(Base):
    # __tablename__: Special attribute that defines the actual database table name
    # This maps the Python class 'Item' to the database table 'items'
    # SQLAlchemy uses this to generate SQL queries for the correct table
    __tablename__ = "items"

    # primary_key=True: Makes this column the primary key (unique identifier for each row)
    # index=True: Creates a database index on this column for faster query performance
    # Indexes speed up SELECT, WHERE, and JOIN operations but use additional storage
    id = Column(Integer, primary_key=True, index=True)

    # index=True on title: Creates an index for faster searching/filtering by title
    # Useful when users frequently search items by title in the FastAPI endpoints
    title = Column(String, index=True)

    # index=True on description: Creates an index for faster searching by description
    # Enables efficient full-text searches on item descriptions
    description = Column(String, index=True)


class Client(Base):

    __tablename__ = "clients"

    id = Column(Integer, primary_key=True, index=True)
    first_name = Column(String(50), index=True)
    last_name = Column(String(50), index=True)
    email = Column(String(100), unique=True, index=True)
    phone_number = Column(String(20), index=True)
    date_of_birth = Column(DateTime)
    gender = Column(String(10))  # e.g., 'Male', 'Female', 'Other'
    address = Column(Text)  # Text for longer addresses
    city = Column(String(50), index=True)
    postal_code = Column(String(20))
    country = Column(String(50), index=True)
    registration_date = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    status = Column(
        String(20), default="active"
    )  # e.g., 'active', 'inactive', 'suspended'
    notes = Column(Text)
    is_verified = Column(Boolean, default=False)
