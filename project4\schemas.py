# Pydantic: A Python library for data validation and parsing using Python type hints
# It automatically validates incoming data, converts types, and provides clear error messages
# Essential for FastAPI to handle request/response data validation and serialization
from datetime import datetime
from pydantic import BaseModel, EmailStr, field_validator
from typing import Optional


# BaseModel: The foundation class for creating Pydantic models/schemas
# Provides automatic validation, serialization, and type conversion based on type hints
# Used to define the structure and validation rules for API request/response data
# ItemBase: Base schema containing common fields shared by all Item-related schemas
# Contains the core data fields that both input and output schemas will use
# Follows DRY principle by defining shared attributes once
class ItemBase(BaseModel):
    title: str
    description: str


# ItemCreate: Schema for creating new items via POST requests
# Inherits from ItemBase to get title and description fields
# Used for request validation when clients send data to create new items
# Note: No 'id' field since the database auto-generates it
class ItemCreate(ItemBase):
    pass


class ItemDelete(ItemBase):
    pass


# Item: Schema for returning item data in API responses
# Includes the 'id' field that gets added after database creation
# Used when FastAPI returns item data to clients (GET requests)
class Item(ItemBase):
    id: int

    # model_config enables attribute-based validation for Pydantic v2
    # This replaces orm_mode=True from Pydantic v1 and allows model_validate(..., from_attributes=True)
    # to convert SQLAlchemy ORM objects to Pydantic models for FastAPI responses.
    model_config = {"from_attributes": True}


class ClientBase(BaseModel):
    first_name: str
    last_name: str
    email: EmailStr
    phone_number: str
    date_of_birth: Optional[datetime] = None
    gender: Optional[str] = None
    address: Optional[str] = None
    city: str
    postal_code: str
    country: str
    registration_date: Optional[datetime] = None
    status: str = "active"
    notes: Optional[str] = None
    is_verified: bool = False

    # Custom validators
    @field_validator("gender")
    def validate_gender(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and v not in ["Male", "Female", "Other"]:
            raise ValueError("Gender must be Male, Female, or Other")
        return v

    @field_validator("status")
    def validate_status(cls, v: str) -> str:
        if not v or v not in ["active", "inactive", "suspended"]:
            raise ValueError("Status must be active, inactive, or suspended")
        return v

    @field_validator("first_name", "last_name")
    def validate_names(cls, v: str) -> str:
        if not v or len(v.strip()) < 2:
            raise ValueError("Names must be at least 2 characters long")
        return v.strip().title()

    @field_validator("phone_number")
    def validate_phone(cls, v: str) -> str:
        cleaned = "".join(filter(str.isdigit, v))
        if len(cleaned) < 10 or len(cleaned) > 15:
            raise ValueError("Phone number must be between 10 and 15 digits")
        return v

    @field_validator("postal_code")
    def validate_postal_code(cls, v: str) -> str:
        if not v or len(v.strip()) < 3:
            raise ValueError("Postal code must be at least 3 characters")
        return v.strip().upper()

    @field_validator("city", "country")
    def validate_location(cls, v: str) -> str:
        if not v or len(v.strip()) < 2:
            raise ValueError("City and country must be at least 2 characters")
        return v.strip().title()

    @field_validator("date_of_birth")
    def validate_date_of_birth(cls, v: Optional[datetime]) -> Optional[datetime]:
        if v is not None:
            if v > datetime.now():
                raise ValueError("Date of birth cannot be in the future")
            # Check if age is reasonable (e.g., not older than 150 years)
            age_limit = datetime.now().replace(year=datetime.now().year - 150)
            if v < age_limit:
                raise ValueError("Date of birth is too old")
        return v


class ClientCreate(ClientBase):
    pass


class ClientDelete(ClientBase):
    pass


class Client(ClientBase):
    id: int

    model_config = {"from_attributes": True}
