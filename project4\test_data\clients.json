[{"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone_number": "+15551234567", "date_of_birth": "1985-07-20T00:00:00", "gender": "Male", "address": "123 Main St", "city": "Springfield", "postal_code": "12345", "country": "USA", "registration_date": "2024-01-15T10:30:00", "status": "active", "notes": null, "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+15559876543", "date_of_birth": "1990-11-05T00:00:00", "gender": "Female", "address": "456 Oak Ave", "city": "Metropolis", "postal_code": "54321", "country": "USA", "registration_date": "2023-12-01T09:00:00", "status": "active", "notes": "Preferred contact via email", "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+44 20 7946 0958", "date_of_birth": "2000-03-12T00:00:00", "gender": "Other", "address": "789 Maple Rd", "city": "London", "postal_code": "SW1A 1AA", "country": "UK", "registration_date": "2025-02-20T14:45:00", "status": "active", "notes": null, "is_verified": false}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+61 2 9374 4000", "date_of_birth": "1978-09-30T00:00:00", "gender": "Female", "address": "101 Harbour St", "city": "Sydney", "postal_code": "2000", "country": "Australia", "registration_date": "2023-08-10T11:20:00", "status": "inactive", "notes": "Temporarily inactive", "is_verified": false}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "micha<PERSON>.<EMAIL>", "phone_number": "******-555-0143", "date_of_birth": "1965-01-15T00:00:00", "gender": "Male", "address": "1600 Pennsylvania Ave NW", "city": "Washington", "postal_code": "20500", "country": "USA", "registration_date": "2023-07-04T16:00:00", "status": "active", "notes": null, "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+49 30 123456", "date_of_birth": "1995-06-22T00:00:00", "gender": "Female", "address": "<PERSON><PERSON> den Linden 77", "city": "Berlin", "postal_code": "10117", "country": "Germany", "registration_date": "2024-03-15T08:30:00", "status": "active", "notes": "VIP client", "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+34 91 1234567", "date_of_birth": "1988-02-18T00:00:00", "gender": "Male", "address": "Calle Gran Vía 1", "city": "Madrid", "postal_code": "28013", "country": "Spain", "registration_date": "2024-05-05T12:00:00", "status": "suspended", "notes": "Payment issues", "is_verified": false}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+52 55 1234 5678", "date_of_birth": "1972-12-03T00:00:00", "gender": "Female", "address": "Av. Reforma 222", "city": "Mexico City", "postal_code": "06600", "country": "Mexico", "registration_date": "2025-06-01T09:15:00", "status": "active", "notes": null, "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "****** 555 0198", "date_of_birth": "1983-04-27T00:00:00", "gender": "Male", "address": "100 Queen St", "city": "Toronto", "postal_code": "M5H 2N2", "country": "Canada", "registration_date": "2024-09-30T10:00:00", "status": "inactive", "notes": "Requested account deletion", "is_verified": false}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+82 2 3456 7890", "date_of_birth": "1992-08-14T00:00:00", "gender": "Female", "address": "456 Gangnam-daero", "city": "Seoul", "postal_code": "04521", "country": "South Korea", "registration_date": "2023-11-21T15:30:00", "status": "active", "notes": null, "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+82-10-1234-5678", "date_of_birth": "1987-10-01T00:00:00", "gender": "Male", "address": "123 Gangnam-daero", "city": "Seoul", "postal_code": "06022", "country": "South Korea", "registration_date": "2025-01-10T13:00:00", "status": "active", "notes": "Has newsletter subscription", "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+86 10 ********", "date_of_birth": "2002-05-19T00:00:00", "gender": "Female", "address": "1 Beijing Rd", "city": "Beijing", "postal_code": "100000", "country": "China", "registration_date": "2024-07-07T17:45:00", "status": "active", "notes": null, "is_verified": false}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+33 1 23 45 67 89", "date_of_birth": "1955-03-05T00:00:00", "gender": "Male", "address": "10 Rue de Rivoli", "city": "Paris", "postal_code": "75004", "country": "France", "registration_date": "2024-12-12T14:00:00", "status": "active", "notes": "Long-term client", "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+****************", "date_of_birth": "1968-07-07T00:00:00", "gender": "Female", "address": "200 Santa Monica Blvd", "city": "Los Angeles", "postal_code": "90001", "country": "USA", "registration_date": "2023-05-20T10:20:00", "status": "suspended", "notes": "Requires follow-up", "is_verified": false}, {"first_name": "<PERSON>", "last_name": "White", "email": "<EMAIL>", "phone_number": "+44 ************", "date_of_birth": "1999-11-11T00:00:00", "gender": "Male", "address": "Piccadilly Circus", "city": "Manchester", "postal_code": "M1 1AA", "country": "UK", "registration_date": "2025-04-01T09:00:00", "status": "active", "notes": null, "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "******-555-0133", "date_of_birth": "1980-02-29T00:00:00", "gender": "Female", "address": "50 Fremont St", "city": "San Francisco", "postal_code": "94105", "country": "USA", "registration_date": "2024-10-10T11:11:00", "status": "active", "notes": "Loyalty member", "is_verified": true}, {"first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "+61 3 9010 1234", "date_of_birth": null, "gender": null, "address": null, "city": "Melbourne", "postal_code": "3000", "country": "Australia", "registration_date": null, "status": "inactive", "notes": null, "is_verified": false}]