# Project 5: Same as Project 4 but with comprehensive list of public methods in sqlalchemy.orm.Session

- This project builds upon the previous project (Project 4) but with comprehensive list of public methods in sqlalchemy.orm.Session

- reference: [sqlalchemy_session_methods_reference.md](./sqlalchemy_session_methods_reference.md)

## Steps

1. Set up a virtual environment

   ```PowerShell
   py -m venv project5_v_env
   .\project5_v_env\Scripts\activate
   ```

2. Install the required dependencies

   ```PowerShell
    pip install -r requirements.txt
    ```