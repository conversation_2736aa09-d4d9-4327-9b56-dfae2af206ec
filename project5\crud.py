from typing import List, Dict, Any
from sqlalchemy.orm import Session
import models, schemas

# CRUD: Create, Read, Update, Delete operations for the Item model
# These functions provide a clean interface between FastAPI endpoints and the database


# db: Session - This is Python type hinting syntax
# 'db' is the parameter name, 'Session' is the type annotation
# Type hints improve code readability, enable IDE autocomplete, and help catch errors
# Here it tells us that 'db' should be a SQLAlchemy Session object
def get_item(db: Session, item_id: int):
    # Uses the Session to query the database for a specific item by ID
    # .filter() adds a WHERE clause, .first() returns the first match or None
    return db.query(models.Item).filter(models.Item.id == item_id).first()


# skip and limit parameters enable pagination for large datasets
def get_items(db: Session, skip: int = 0, limit: int = 10):
    # .offset(skip) skips the first 'skip' records (for pagination)
    # .limit(limit) restricts the number of returned records
    # .all() executes the query and returns all matching records as a list
    return db.query(models.Item).offset(skip).limit(limit).all()


# item: schemas.ItemCreate - expects a Pydantic schema for data validation
def create_item(db: Session, item: schemas.ItemCreate):
    # Convert Pydantic schema to dictionary using model_dump()
    # **item.model_dump() unpacks the dictionary as keyword arguments to create the SQLAlchemy model
    db_item = models.Item(**item.model_dump())

    # Add the new item to the session (stages it for database insertion)
    db.add(db_item)

    # Commit the transaction to actually save the item to the database
    db.commit()

    # Refresh the object to get the auto-generated ID from the database
    db.refresh(db_item)

    return db_item


def create_items_batch(db: Session, items: List[schemas.ItemCreate]):
    """
    Create multiple items in a single database transaction.

    Args:
        db: SQLAlchemy database session
        items: List of ItemCreate schema objects

    Returns:
        List of created Item database objects
    """
    # convert all Pydantic schemas to SQLAlchemy models
    db_items = [models.Item(**item.model_dump()) for item in items]

    # Add all items to the session in one operation
    db.add_all(db_items)

    # Commit all changes in a single transaction
    db.commit()

    # Refresh all objects to get their database-generated values
    for item in db_items:
        db.refresh(item)
    return db_items


def delete_item(db: Session, item_id: int):
    db_item = db.query(models.Item).filter(models.Item.id == item_id).first()
    if db_item is None:
        return None

    # Create a copy of the client data (the SQLAlchemy object is still tracked by the session)
    item_copy = db_item

    # Delete the client
    db.delete(item_copy)
    db.commit()

    # Return the copy (no need to refresh as the object is deleted from DB)
    return item_copy


def delete_items_batch(db: Session, items: List[schemas.Item]):
    """
    Delete multiple items in a single database transaction.

    Args:
        db: SQLAlchemy database session
        items: List of ItemDelete schema objects (must have 'id' attribute)

    Returns:
        Number of items deleted
    """
    # Extract item IDs from the input list
    item_ids = [item.id for item in items]
    if not item_ids:
        return 0

    # Bulk delete using filter and in_ operator
    deleted_count = (
        db.query(models.Item)
        .filter(models.Item.id.in_(item_ids))
        .delete(synchronize_session=False)
    )
    db.commit()
    return deleted_count


# Delete all items from the items table
def delete_all_items(db: Session) -> List[schemas.Item]:
    """
    Delete all items from the items table.
    Returns a list of deleted items (before deletion).
    """
    items = db.query(models.Item).all()
    if not items:
        return []
    # Store items to return after deletion
    deleted_items = [schemas.Item.model_validate(item) for item in items]
    db.query(models.Item).delete(synchronize_session=False)
    db.commit()
    return deleted_items


def get_client(db: Session, client_id: int):
    return db.query(models.Client).filter(models.Client.id == client_id).first()


def get_clients(db: Session, skip: int = 0, limit: int = 15):
    return db.query(models.Client).offset(skip).limit(limit).all()


def create_client(db: Session, client: schemas.ClientCreate):
    db_client = models.Client(**client.model_dump())
    db.add(db_client)
    db.commit()
    db.refresh(db_client)
    return db_client


def delete_client(db: Session, client_id: int):
    db_client = db.query(models.Client).filter(models.Client.id == client_id).first()
    if db_client is None:
        return None

    # Create a copy of the client data (the SQLAlchemy object is still tracked by the session)
    client_copy = db_client

    # Delete the client
    db.delete(db_client)
    db.commit()

    # Return the copy (no need to refresh as the object is deleted from DB)
    return client_copy


def create_clients_batch(db: Session, clients: List[schemas.ClientCreate]):
    """
    Create multiple clients in a single database transaction.

    Args:
        db: SQLAlchemy database session
        clients: List of ClientCreate schema objects

    Returns:
        List of created Client database objects
    """
    # convert all Pydantic schemas to SQLAlchemy models
    db_clients = [models.Client(**client.model_dump()) for client in clients]

    # Add all clients to the session in one operation
    db.add_all(db_clients)

    # Commit all changes in a single transaction
    db.commit()

    # Refresh all objects to get their database-generated values
    for client in db_clients:
        db.refresh(client)
    return db_clients


def delete_all_clients(db: Session) -> List[schemas.Client]:
    """
    Delete all clients from the clients table.
    Returns a list of deleted clients (before deletion).
    """
    clients = db.query(models.Client).all()
    if not clients:
        return []
    # Store clients to return after deletion
    deleted_clients = [schemas.Client.model_validate(client) for client in clients]
    db.query(models.Client).delete(synchronize_session=False)
    db.commit()
    return deleted_clients


def import_clients_from_json(
    db: Session, clients_data: List[Dict[str, Any]]
) -> List[models.Client]:
    """
    Import a list of clients from JSON data (list of dicts), validate, and insert into DB.
    Args:
        db: SQLAlchemy session
        clients_data: List of dicts representing clients
    Returns:
        List of created Client DB objects
    Raises:
        ValueError: If any client data is invalid
    """
    # Validate each client using the Pydantic schema
    clients = [schemas.ClientCreate(**client) for client in clients_data]
    db_clients = [models.Client(**client.model_dump()) for client in clients]
    db.add_all(db_clients)
    db.commit()
    for client in db_clients:
        db.refresh(client)
    return db_clients
