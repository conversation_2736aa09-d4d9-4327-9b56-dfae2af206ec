# sqlalchemy.orm.Session

- Here is a **comprehensive list of public methods** in `sqlalchemy.orm.Session` (from `sqlalchemy.orm import Session`) as of **SQLAlchemy 2.0+** — particularly focusing on **core, commonly used methods** and omitting private/internal ones (e.g., `_merge`, `_connection_for_bind`).

## 🔁 Transaction & Session Lifecycle

| Method             | Description                                        |
| ------------------ | -------------------------------------------------- |
| `begin()`          | Begin a new transaction block.                     |
| `commit()`         | Commit the current transaction.                    |
| `rollback()`       | Roll back the current transaction.                 |
| `close()`          | Close the session (releases connection resources). |
| `flush()`          | Flush pending changes to the database.             |
| `expire_all()`     | Expire all persistent instances in the session.    |
| `expire(instance)` | Expire a single instance.                          |
| `invalidate()`     | Invalidate the session (disconnect from DB).       |

## 💾 CRUD Operations

| Method                          | Description                                      |
| ------------------------------- | ------------------------------------------------ |
| `add(instance)`                 | Add an instance to the session.                  |
| `add_all([instances])`          | Add multiple instances at once.                  |
| `merge(instance)`               | Merge a detached instance into the session.      |
| `delete(instance)`              | Mark an instance for deletion.                   |
| `get(EntityClass, primary_key)` | Get an instance by primary key.                  |
| `get_or_404(...)`               | (Optional depending on extensions) Get or raise. |
| `execute(statement)`            | Execute a SQL expression or ORM statement.       |

## 🔍 Querying

| Method                 | Description                                         |
| ---------------------- | --------------------------------------------------- |
| `scalar(select_stmt)`  | Execute a `select()` and return scalar value.       |
| `scalars(select_stmt)` | Execute a `select()` and yield scalar results.      |
| `execute(select_stmt)` | General-purpose statement execution.                |
| `query(...)`           | (Legacy) ORM-style querying; use `select()` in 2.0. |
| `get()`                | Shortcut to retrieve by primary key.                |

## 📌 Object State Management

| Method                       | Description                                   |
| ---------------------------- | --------------------------------------------- |
| `is_modified(instance)`      | Check if an instance has pending changes.     |
| `refresh(instance)`          | Reload instance from the database.            |
| `expunge(instance)`          | Remove instance from the session.             |
| `expunge_all()`              | Remove all instances from the session.        |
| `detach(instance)`           | Synonym for `expunge`.                        |
| `identity_key(instance=...)` | Return identity key for an instance or class. |

## 🧱 Context Managers

| Method           | Description                                 |
| ---------------- | ------------------------------------------- |
| `begin()`        | As context manager: `with session.begin():` |
| `begin_nested()` | Start a nested (SAVEPOINT) transaction.     |
| `no_autoflush()` | Temporarily disable autoflush in a block.   |

## 🔌 Engine & Connection

| Method         | Description                             |
| -------------- | --------------------------------------- |
| `get_bind()`   | Return the DB connection bind in use.   |
| `connection()` | Get a DBAPI connection for raw SQL use. |

## 🧠 ORM Internals & Events (Advanced)

| Method               | Description                                   |
| -------------------- | --------------------------------------------- |
| `bind_mapper(...)`   | Bind a specific mapper to an engine.          |
| `bind_table(...)`    | Bind a specific table to an engine.           |
| `info` (property)    | Dict for storing user-level session metadata. |
| `dirty` (property)   | Set of modified instances.                    |
| `new` (property)     | Set of new instances added but not flushed.   |
| `deleted` (property) | Set of instances marked for deletion.         |
| `identity_map`       | The identity map (cache) of the session.      |

## ✅ Recommended SQLAlchemy 2.0 API Usage

SQLAlchemy 2.0 encourages usage of the **`select()`, `insert()`, `update()`, `delete()` constructs** and the new `Session.execute()` for uniform access. The older `session.query()` is **deprecated** but still functional.

---

Would you like:

- A comparison with the legacy `Query` object?
- A diagram of how session handles state transitions?
- A full list (via `dir(Session)`) including private/internal methods?

Let me know and I’ll tailor the output accordingly.
